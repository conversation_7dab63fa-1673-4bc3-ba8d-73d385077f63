"use client";

import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { Controller } from "react-hook-form";
import { cn } from "@/lib/utils";

interface SectionInformationProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

const SectionInformation = ({ control, errors }: SectionInformationProps) => {
  return (
    <div className="bg-white">
      <h2 className="text-lg font-medium text-gray-900 mb-6">
        Section Information
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-5 gap-y-3">
        {/* Section Name */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Section Name
          </label>
          <Controller
            name="sectionName"
            control={control}
            render={({ field }) => (
              <BaseInput
                {...field}
                placeholder="Input section name"
                className={errors.sectionName ? "border-red-500" : ""}
              />
            )}
          />
          {errors.sectionName && (
            <p className="text-red-500 text-sm mt-1">
              {errors.sectionName.message}
            </p>
          )}
        </div>

        {/* Section ID */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Section ID
          </label>
          <Controller
            name="sectionId"
            control={control}
            render={({ field }) => (
              <BaseInput
                {...field}
                placeholder="ID"
                className={errors.sectionId ? "border-red-500" : ""}
                disabled
              />
            )}
          />
          {errors.sectionId && (
            <p className="text-red-500 text-sm mt-1">
              {errors.sectionId.message}
            </p>
          )}
        </div>

        {/* Section Type */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Section Type
          </label>
          <Controller
            name="sectionType"
            control={control}
            render={({ field }) => (
              <BaseSelect value={field.value} onValueChange={field.onChange}>
                <BaseSelectTrigger
                  className={cn(
                    "w-full",
                    errors.sectionType ? "border-red-500" : ""
                  )}
                >
                  <BaseSelectValue placeholder="Select section type" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  <BaseSelectItem value="video">Video</BaseSelectItem>
                  <BaseSelectItem value="audio">Audio</BaseSelectItem>
                  <BaseSelectItem value="document">PDF</BaseSelectItem>
                  <BaseSelectItem value="quiz">Quiz</BaseSelectItem>
                </BaseSelectContent>
              </BaseSelect>
            )}
          />
          {errors.sectionType && (
            <p className="text-red-500 text-sm mt-1">
              {errors.sectionType.message}
            </p>
          )}
        </div>

        {/* Material Selection or Quiz Settings */}
        <div>
          <Controller
            name="sectionType"
            control={control}
            render={({ field: { value: sectionType } }) => {
              if (sectionType === "quiz") {
                return (
                  <div className="grid grid-cols-2 gap-4">
                    {/* Passing Grade */}
                    <div>
                      <label className="block text-xs font-medium text-comp-content-primary mb-1">
                        Passing Grade
                      </label>
                      <Controller
                        name="passingGrade"
                        control={control}
                        render={({ field }) => (
                          <BaseInput
                            {...field}
                            type="number"
                            placeholder="Input passing grade"
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                            className={
                              errors.passingGrade ? "border-red-500" : ""
                            }
                          />
                        )}
                      />
                      {errors.passingGrade && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.passingGrade.message}
                        </p>
                      )}
                    </div>

                    {/* Number of Questions */}
                    <div>
                      <label className="block text-xs font-medium text-comp-content-primary mb-1">
                        Number of Questions
                      </label>
                      <Controller
                        name="numberOfQuestions"
                        control={control}
                        render={({ field }) => (
                          <BaseInput
                            {...field}
                            type="number"
                            placeholder="Input number of questions"
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                            className={
                              errors.numberOfQuestions ? "border-red-500" : ""
                            }
                          />
                        )}
                      />
                      {errors.numberOfQuestions && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.numberOfQuestions.message}
                        </p>
                      )}
                    </div>
                  </div>
                );
              }

              // Material selection for video, audio, document
              const materialLabels = {
                video: "Video Materials",
                audio: "Audio Materials",
                document: "Document Materials",
              };

              const materialPlaceholders = {
                video: "Select video from the library",
                audio: "Select audio from the library",
                document: "Select document from the library",
              };

              return (
                <div>
                  <label className="block text-xs font-medium text-comp-content-primary mb-1">
                    {materialLabels[sectionType as keyof typeof materialLabels]}
                  </label>
                  <Controller
                    name="materialId"
                    control={control}
                    render={({ field }) => (
                      <BaseSelect
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <BaseSelectTrigger
                          className={cn(
                            "w-full",
                            errors.materialId ? "border-red-500" : ""
                          )}
                        >
                          <BaseSelectValue
                            placeholder={
                              materialPlaceholders[
                                sectionType as keyof typeof materialPlaceholders
                              ]
                            }
                          />
                        </BaseSelectTrigger>
                        <BaseSelectContent>
                          <BaseSelectItem value="material-1">
                            Sample Material 1
                          </BaseSelectItem>
                          <BaseSelectItem value="material-2">
                            Sample Material 2
                          </BaseSelectItem>
                          <BaseSelectItem value="material-3">
                            Sample Material 3
                          </BaseSelectItem>
                        </BaseSelectContent>
                      </BaseSelect>
                    )}
                  />
                  {errors.materialId && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.materialId.message}
                    </p>
                  )}
                </div>
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default SectionInformation;
