"use client";

import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import VideoPreview from "@/components/molecules/admin/manage-material/preview/video-preview";
import AudioPreview from "@/components/molecules/admin/manage-material/preview/audio-preview";
import DocumentPreview from "@/components/molecules/admin/manage-material/document-preview";
import QuizTable from "./quiz-table";

interface SectionPreviewProps {
  sectionType: "video" | "audio" | "document" | "quiz";
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

const SectionPreview = ({
  sectionType,
  control,
  errors,
}: SectionPreviewProps) => {
  const renderPreviewContent = () => {
    switch (sectionType) {
      case "video":
        return (
          <div className="h-[400px]">
            <VideoPreview material={null} />
          </div>
        );
      case "audio":
        return (
          <div className="h-[400px]">
            <AudioPreview material={null} />
          </div>
        );
      case "document":
        return (
          <div className="h-[400px]">
            <DocumentPreview material={null} />
          </div>
        );
      case "quiz":
        return <QuizTable control={control} errors={errors} />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white">
      <h2 className="text-sm font-semibold text-comp-content-primary mb-3">
        {sectionType === "quiz" ? "Quiz Question" : "Preview"}
      </h2>

      <div className="w-full">{renderPreviewContent()}</div>
    </div>
  );
};

export default SectionPreview;
