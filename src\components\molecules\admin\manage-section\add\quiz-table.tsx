"use client";

import React, { useState } from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import { BaseButton } from "@/components/atoms/button";
import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { Search, RefreshCw, Plus, ChevronDown } from "lucide-react";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";

interface QuizTableProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

// Dummy quiz data for demonstration
const dummyQuizData = [
  {
    id: "12345",
    category: "2 Category",
    level: "4 Levels",
    questionType: "<PERSON><PERSON><PERSON>",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
    optionD: "Informasi Tambahan",
    keyAnswer: "Informasi Tambahan",
  },
  {
    id: "12346",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
    optionD: "Informasi Tambahan",
    keyAnswer: "Informasi Tambahan",
  },
  {
    id: "12347",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
    optionD: "Informasi Tambahan",
    keyAnswer: "Informasi Tambahan",
  },
];

const QuizTable = ({ control, errors }: QuizTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [showAddDropdown, setShowAddDropdown] = useState(false);
  const [quizQuestions, setQuizQuestions] = useState<typeof dummyQuizData>([]);

  const handleAddFromBank = () => {
    console.log("Add from Question Bank");
    setShowAddDropdown(false);
  };

  const handleAddFromTemplate = () => {
    console.log("Add from Question Template");
    setShowAddDropdown(false);
  };

  return (
    <div className="space-y-4">
      {/* Filter Section */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <BaseSelect
            value={selectedCategory}
            onValueChange={setSelectedCategory}
          >
            <BaseSelectTrigger className="w-[200px]">
              <BaseSelectValue placeholder="Search by" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="category">Category</BaseSelectItem>
              <BaseSelectItem value="level">Level</BaseSelectItem>
              <BaseSelectItem value="type">Question Type</BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>

        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <BaseInput
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <BaseButton
          type="button"
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          Reset
        </BaseButton>

        <div className="relative">
          <BaseButton
            type="button"
            className="bg-orange-500 hover:bg-orange-600 flex items-center gap-2"
            onClick={() => setShowAddDropdown(!showAddDropdown)}
          >
            <Plus className="w-4 h-4" />
            Add New Question
            <ChevronDown className="w-4 h-4" />
          </BaseButton>

          {showAddDropdown && (
            <div className="absolute right-0 top-full mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              <div className="p-2">
                <button
                  onClick={handleAddFromBank}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                >
                  Add from Question Bank
                </button>
                <button
                  onClick={handleAddFromTemplate}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                >
                  Add from Question Template
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <BaseTable>
          <BaseTableHeader>
            <BaseTableRow className="bg-gray-50">
              <BaseTableHead className="w-20">Question ID</BaseTableHead>
              <BaseTableHead className="w-24">Category</BaseTableHead>
              <BaseTableHead className="w-20">Level</BaseTableHead>
              <BaseTableHead className="w-28">Question Type</BaseTableHead>
              <BaseTableHead className="min-w-[200px]">Question</BaseTableHead>
              <BaseTableHead className="w-24">Option A</BaseTableHead>
              <BaseTableHead className="w-24">Option B</BaseTableHead>
              <BaseTableHead className="w-24">Option C</BaseTableHead>
              <BaseTableHead className="w-24">Option D</BaseTableHead>
              <BaseTableHead className="w-28">Key Answer</BaseTableHead>
              <BaseTableHead className="w-20">Action</BaseTableHead>
            </BaseTableRow>
          </BaseTableHeader>
          <BaseTableBody>
            {quizQuestions.length === 0 ? (
              <BaseTableRow>
                <BaseTableCell
                  colSpan={11}
                  className="text-center py-8 text-gray-500"
                >
                  No quiz questions added yet. Click "Add New Question" to get
                  started.
                </BaseTableCell>
              </BaseTableRow>
            ) : (
              quizQuestions.map((quiz, index) => (
                <BaseTableRow key={index}>
                  <BaseTableCell className="font-medium">
                    {quiz.id}
                  </BaseTableCell>
                  <BaseTableCell>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                      {quiz.category}
                    </span>
                  </BaseTableCell>
                  <BaseTableCell>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                      {quiz.level}
                    </span>
                  </BaseTableCell>
                  <BaseTableCell>{quiz.questionType}</BaseTableCell>
                  <BaseTableCell
                    className="max-w-[200px] truncate"
                    title={quiz.question}
                  >
                    {quiz.question}
                  </BaseTableCell>
                  <BaseTableCell
                    className="max-w-[100px] truncate"
                    title={quiz.optionA}
                  >
                    {quiz.optionA}
                  </BaseTableCell>
                  <BaseTableCell
                    className="max-w-[100px] truncate"
                    title={quiz.optionB}
                  >
                    {quiz.optionB}
                  </BaseTableCell>
                  <BaseTableCell
                    className="max-w-[100px] truncate"
                    title={quiz.optionC}
                  >
                    {quiz.optionC}
                  </BaseTableCell>
                  <BaseTableCell
                    className="max-w-[100px] truncate"
                    title={quiz.optionD}
                  >
                    {quiz.optionD}
                  </BaseTableCell>
                  <BaseTableCell
                    className="max-w-[100px] truncate"
                    title={quiz.keyAnswer}
                  >
                    {quiz.keyAnswer}
                  </BaseTableCell>
                  <BaseTableCell>
                    <BaseButton
                      type="button"
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      Remove
                    </BaseButton>
                  </BaseTableCell>
                </BaseTableRow>
              ))
            )}
          </BaseTableBody>
        </BaseTable>
      </div>

      {/* Pagination */}
      {quizQuestions.length > 0 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700">
            Showing 1 to 10 of 8,043 entries
          </p>
          <div className="flex items-center gap-2">
            <BaseButton variant="outline" size="sm" disabled>
              Previous
            </BaseButton>
            <BaseButton
              variant="outline"
              size="sm"
              className="bg-orange-500 text-white"
            >
              1
            </BaseButton>
            <BaseButton variant="outline" size="sm">
              2
            </BaseButton>
            <BaseButton variant="outline" size="sm">
              3
            </BaseButton>
            <span className="px-2">...</span>
            <BaseButton variant="outline" size="sm">
              10
            </BaseButton>
            <BaseButton variant="outline" size="sm">
              Next
            </BaseButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuizTable;
